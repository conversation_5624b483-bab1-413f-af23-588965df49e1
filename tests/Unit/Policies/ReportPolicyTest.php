<?php

declare(strict_types=1);

namespace Tests\Unit\Policies;

use App\Models\Organisation;
use App\Models\Role;
use App\Models\User;
use App\Policies\ReportPolicy;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

/**
 * Report Policy Test
 *
 * Tests for report access permissions based on user roles and organisation membership
 */
final class ReportPolicyTest extends TestCase
{
    use RefreshDatabase;

    private ReportPolicy $policy;
    private User $systemRootUser;
    private User $systemAdminUser;
    private User $ownerUser;
    private User $memberUser;
    private User $unrelatedUser;
    private Organisation $organisation;
    private Organisation $anotherOrganisation;
    private Role $systemRootRole;
    private Role $systemAdminRole;
    private Role $ownerRole;
    private Role $memberRole;

    protected function setUp(): void
    {
        parent::setUp();

        $this->policy = new ReportPolicy();

        // Create organisations
        $this->organisation = Organisation::factory()->create([
            'name' => 'Test Organisation',
            'status' => 'active',
        ]);

        $this->anotherOrganisation = Organisation::factory()->create([
            'name' => 'Another Organisation',
            'status' => 'active',
        ]);

        // Create system roles
        $this->systemRootRole = Role::create([
            'name' => 'root',
            'guard_name' => 'system',
            'organisation_id' => null,
        ]);

        $this->systemAdminRole = Role::create([
            'name' => 'admin',
            'guard_name' => 'system',
            'organisation_id' => null,
        ]);

        // Create organisation roles
        $this->ownerRole = Role::create([
            'name' => 'owner',
            'guard_name' => 'api',
            'organisation_id' => $this->organisation->id,
        ]);

        $this->memberRole = Role::create([
            'name' => 'member',
            'guard_name' => 'api',
            'organisation_id' => $this->organisation->id,
        ]);

        // Create users
        $this->systemRootUser = User::factory()->create(['name' => 'System Root']);
        $this->systemAdminUser = User::factory()->create(['name' => 'System Admin']);
        $this->ownerUser = User::factory()->create(['name' => 'Owner User']);
        $this->memberUser = User::factory()->create(['name' => 'Member User']);
        $this->unrelatedUser = User::factory()->create(['name' => 'Unrelated User']);

        // Assign system roles
        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId(null);
        $this->systemRootUser->assignRole($this->systemRootRole);
        $this->systemAdminUser->assignRole($this->systemAdminRole);

        // Associate users with organisation and assign roles
        $this->ownerUser->organisations()->attach($this->organisation->id);
        $this->memberUser->organisations()->attach($this->organisation->id);

        app(\Spatie\Permission\PermissionRegistrar::class)->setPermissionsTeamId($this->organisation->id);
        $this->ownerUser->assignRole($this->ownerRole);
        $this->memberUser->assignRole($this->memberRole);
    }

    // ========================================
    // viewAny Tests
    // ========================================

    public function test_system_root_can_view_any_reports(): void
    {
        $this->assertTrue($this->policy->viewAny($this->systemRootUser));
    }

    public function test_system_admin_can_view_any_reports(): void
    {
        $this->assertTrue($this->policy->viewAny($this->systemAdminUser));
    }

    public function test_organisation_owner_can_view_reports(): void
    {
        $this->assertTrue($this->policy->viewAny($this->ownerUser));
    }

    public function test_organisation_member_can_view_reports(): void
    {
        $this->assertTrue($this->policy->viewAny($this->memberUser));
    }

    public function test_unrelated_user_cannot_view_reports(): void
    {
        $this->assertFalse($this->policy->viewAny($this->unrelatedUser));
    }

    // ========================================
    // viewForOrganisations Tests
    // ========================================

    public function test_system_root_can_view_any_organisation_reports(): void
    {
        $this->assertTrue($this->policy->viewForOrganisations($this->systemRootUser, [$this->organisation->id]));
        $this->assertTrue($this->policy->viewForOrganisations($this->systemRootUser, [$this->anotherOrganisation->id]));
        $this->assertTrue($this->policy->viewForOrganisations($this->systemRootUser, [$this->organisation->id, $this->anotherOrganisation->id]));
    }

    public function test_system_admin_can_view_any_organisation_reports(): void
    {
        $this->assertTrue($this->policy->viewForOrganisations($this->systemAdminUser, [$this->organisation->id]));
        $this->assertTrue($this->policy->viewForOrganisations($this->systemAdminUser, [$this->anotherOrganisation->id]));
        $this->assertTrue($this->policy->viewForOrganisations($this->systemAdminUser, [$this->organisation->id, $this->anotherOrganisation->id]));
    }

    public function test_organisation_owner_can_view_own_organisation_reports(): void
    {
        $this->assertTrue($this->policy->viewForOrganisations($this->ownerUser, [$this->organisation->id]));
    }

    public function test_organisation_owner_cannot_view_other_organisation_reports(): void
    {
        $this->assertFalse($this->policy->viewForOrganisations($this->ownerUser, [$this->anotherOrganisation->id]));
    }

    public function test_organisation_owner_cannot_view_multiple_organisations(): void
    {
        $this->assertFalse($this->policy->viewForOrganisations($this->ownerUser, [$this->organisation->id, $this->anotherOrganisation->id]));
    }

    public function test_organisation_member_can_view_own_organisation_reports(): void
    {
        $this->assertTrue($this->policy->viewForOrganisations($this->memberUser, [$this->organisation->id]));
    }

    public function test_organisation_member_cannot_view_other_organisation_reports(): void
    {
        $this->assertFalse($this->policy->viewForOrganisations($this->memberUser, [$this->anotherOrganisation->id]));
    }

    public function test_organisation_member_cannot_view_multiple_organisations(): void
    {
        $this->assertFalse($this->policy->viewForOrganisations($this->memberUser, [$this->organisation->id, $this->anotherOrganisation->id]));
    }

    public function test_unrelated_user_cannot_view_any_organisation_reports(): void
    {
        $this->assertFalse($this->policy->viewForOrganisations($this->unrelatedUser, [$this->organisation->id]));
        $this->assertFalse($this->policy->viewForOrganisations($this->unrelatedUser, [$this->anotherOrganisation->id]));
    }

    // ========================================
    // Individual Report Type Tests
    // ========================================

    public function test_viewSales_follows_viewAny_logic(): void
    {
        $this->assertTrue($this->policy->viewSales($this->systemRootUser));
        $this->assertTrue($this->policy->viewSales($this->systemAdminUser));
        $this->assertTrue($this->policy->viewSales($this->ownerUser));
        $this->assertTrue($this->policy->viewSales($this->memberUser));
        $this->assertFalse($this->policy->viewSales($this->unrelatedUser));
    }

    public function test_viewVolume_follows_viewAny_logic(): void
    {
        $this->assertTrue($this->policy->viewVolume($this->systemRootUser));
        $this->assertTrue($this->policy->viewVolume($this->systemAdminUser));
        $this->assertTrue($this->policy->viewVolume($this->ownerUser));
        $this->assertTrue($this->policy->viewVolume($this->memberUser));
        $this->assertFalse($this->policy->viewVolume($this->unrelatedUser));
    }

    public function test_viewRefunds_follows_viewAny_logic(): void
    {
        $this->assertTrue($this->policy->viewRefunds($this->systemRootUser));
        $this->assertTrue($this->policy->viewRefunds($this->systemAdminUser));
        $this->assertTrue($this->policy->viewRefunds($this->ownerUser));
        $this->assertTrue($this->policy->viewRefunds($this->memberUser));
        $this->assertFalse($this->policy->viewRefunds($this->unrelatedUser));
    }

    public function test_viewOrderStatus_follows_viewAny_logic(): void
    {
        $this->assertTrue($this->policy->viewOrderStatus($this->systemRootUser));
        $this->assertTrue($this->policy->viewOrderStatus($this->systemAdminUser));
        $this->assertTrue($this->policy->viewOrderStatus($this->ownerUser));
        $this->assertTrue($this->policy->viewOrderStatus($this->memberUser));
        $this->assertFalse($this->policy->viewOrderStatus($this->unrelatedUser));
    }

    // ========================================
    // Export Permission Tests
    // ========================================

    public function test_system_root_can_export_reports(): void
    {
        $this->assertTrue($this->policy->export($this->systemRootUser));
    }

    public function test_system_admin_can_export_reports(): void
    {
        $this->assertTrue($this->policy->export($this->systemAdminUser));
    }

    public function test_organisation_owner_can_export_reports(): void
    {
        $this->assertTrue($this->policy->export($this->ownerUser));
    }

    public function test_organisation_member_cannot_export_reports(): void
    {
        $this->assertFalse($this->policy->export($this->memberUser));
    }

    public function test_unrelated_user_cannot_export_reports(): void
    {
        $this->assertFalse($this->policy->export($this->unrelatedUser));
    }
}
